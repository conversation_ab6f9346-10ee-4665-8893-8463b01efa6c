import { SessionFeedbackService } from './session-feedback.service';
import { CreateSessionFeedbackDto } from './dto/create-session-feedback.dto';
export declare class SessionFeedbackController {
    private readonly sessionFeedbackService;
    constructor(sessionFeedbackService: SessionFeedbackService);
    getSessionFeedbackList(sessionId: string): Promise<{
        studentName: string;
        studentEmail: string;
        coordinates: null;
        user: {
            id: number;
            createdAt: Date;
            name: string | null;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            password: string;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string[];
            about: string | null;
            isActive: boolean;
            updatedAt: Date;
            isVerified: boolean;
            needsVerification: boolean;
            emailVerified: Date | null;
            emailVerificationCode: string | null;
            codeExpiryDate: Date | null;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
        } | null;
        id: number;
        sessionId: number;
        userId: number | null;
        rating: number;
        comments: string | null;
        ratings: string | null;
        formData: string | null;
        createdAt: Date;
    }[]>;
    getSessionFeedbackListV2(sessionId: string): Promise<{
        id: number;
        userId: number;
        studentName: string | null;
        studentEmail: string | null;
        fullFeedback: string;
        averageRating: number | null;
        scoreLabel: string;
        emoji: string;
    }[]>;
    getStudentFeedbacks(sessionId: string, userId: string): Promise<{
        id: number;
        sessionId: number;
        userId: number;
        rating: number | null;
        comments: string;
        feedback: string;
        sessionComments: string | null;
        trainerComments: string | null;
        teamComments: string | null;
        suggestions: string | null;
        ratings: null;
        formData: null;
        createdAt: Date;
        studentName: string;
        studentEmail: string;
        user: {
            id: number;
            createdAt: Date;
            name: string | null;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            password: string;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string[];
            about: string | null;
            isActive: boolean;
            updatedAt: Date;
            isVerified: boolean;
            needsVerification: boolean;
            emailVerified: Date | null;
            emailVerificationCode: string | null;
            codeExpiryDate: Date | null;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
        };
    }[]>;
    createSessionFeedback(createSessionFeedbackDto: CreateSessionFeedbackDto): Promise<{
        message: string;
        averageRating: number;
    }>;
}
